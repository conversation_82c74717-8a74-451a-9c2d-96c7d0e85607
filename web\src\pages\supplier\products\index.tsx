import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Package,
  Plus,
  Settings,
  Edit3,
  Trash2,
  Eye,
  Grid3X3,
  List,
  BarChart3,
  Save,
  Loader,
  CheckCircle2,
  AlertCircle,
  X,
  Search,
  Filter,
  Star,
  TrendingUp,
  Zap,
  Sparkles,
  Crown,
  Target,
  Award,
  ChevronRight,
  ShoppingBag,
  DollarSign,
  Tag,
  Image as ImageIcon,
  Palette,
  Shirt,
  Coffee,
  Utensils,
  Layers,
  Hash,
  Type,
  ToggleLeft,
  ToggleRight,
  Calendar,
  Clock,
  Store
} from 'lucide-react';
import { useCurrentUserData } from '../../../hooks/useCurrentUserData';
import { useSupplierProducts, Product } from '../../../stores/supplierProductsStore';
import { useSupplierCategories } from '../../../stores/supplierCategoriesStore';
import { useNavigate } from 'react-router-dom';

// Import suppliers data from local data directory
import { suppliersData } from '../../../data/suppliersData';

// Modern Glass Card Component - Proper z-index for header compatibility
const GlassCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  gradient?: string;
  hoverEffect?: boolean;
}> = ({ children, className = '', gradient = 'from-white/10 to-white/5', hoverEffect = true }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={hoverEffect ? {
      y: -8,
      scale: 1.02,
      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)"
    } : {}}
    transition={{ type: "spring", stiffness: 300, damping: 30 }}
    className={`relative bg-gradient-to-br ${gradient} border border-white/30 rounded-3xl shadow-2xl overflow-hidden ${className}`}
    style={{
      zIndex: 10, // Lower z-index to not interfere with header
      position: 'relative',
    }}
  >
    {/* Enhanced Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />

    {/* Subtle inner glow */}
    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />

    {/* Content */}
    <div className="relative z-10">
      {children}
    </div>
  </motion.div>
);

// Simple Background Orb Component - NO BLUR, LOW Z-INDEX
const FloatingOrb: React.FC<{
  size: number;
  color: string;
  delay: number;
  duration: number;
  x: string;
  y: string;
}> = ({ size, color, delay, duration, x, y }) => (
  <motion.div
    className={`absolute rounded-full ${color}`}
    style={{
      width: size,
      height: size,
      left: x,
      top: y,
      opacity: 0.06, // Very low opacity to prevent blur interference
      zIndex: -1, // Behind everything
      pointerEvents: 'none',
    }}
    animate={{
      x: [0, 30, -20, 0],
      y: [0, -20, 30, 0],
      scale: [1, 1.2, 0.8, 1],
    }}
    transition={{
      duration,
      delay,
      repeat: Infinity,
      ease: "easeInOut",
    }}
  />
);

// Simple Particle System Component - Behind everything
const ParticleSystem: React.FC = () => (
  <div className="absolute inset-0 overflow-hidden pointer-events-none" style={{ zIndex: -2 }}>
    {Array.from({ length: 10 }).map((_, i) => (
      <motion.div
        key={i}
        className="absolute w-1 h-1 bg-white rounded-full opacity-10"
        style={{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          pointerEvents: 'none',
        }}
        animate={{
          y: [0, -100, 0],
          opacity: [0, 0.1, 0],
          scale: [0, 1, 0],
        }}
        transition={{
          duration: 5 + Math.random() * 3,
          delay: Math.random() * 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    ))}
  </div>
);

// Helper function to count options for a product
const getProductOptionsCount = (product: Product) => {
  let count = 0;

  // Restaurant options
  if (product.restaurantOptions) {
    count += (product.restaurantOptions.additions?.length || 0);
    count += (product.restaurantOptions.without?.length || 0);
    count += (product.restaurantOptions.sides?.length || 0);
  }

  // Clothing options
  if (product.clothingOptions) {
    count += (product.clothingOptions.sizes?.length || 0);
    count += (product.clothingOptions.colors?.length || 0);
    count += (product.clothingOptions.gallery?.length || 0);
  }

  // Custom options
  if (product.customOptions) {
    count += product.customOptions.length;
  }

  return count;
};

// Helper function to get product options summary
const getProductOptionsSummary = (product: Product) => {
  const summary: string[] = [];

  // Restaurant options
  if (product.restaurantOptions) {
    if (product.restaurantOptions.additions?.length) {
      summary.push(`${product.restaurantOptions.additions.length} additions`);
    }
    if (product.restaurantOptions.without?.length) {
      summary.push(`${product.restaurantOptions.without.length} without options`);
    }
    if (product.restaurantOptions.sides?.length) {
      summary.push(`${product.restaurantOptions.sides.length} sides`);
    }
  }

  // Clothing options
  if (product.clothingOptions) {
    if (product.clothingOptions.sizes?.length) {
      summary.push(`${product.clothingOptions.sizes.length} sizes`);
    }
    if (product.clothingOptions.colors?.length) {
      summary.push(`${product.clothingOptions.colors.length} colors`);
    }
    if (product.clothingOptions.gallery?.length) {
      summary.push(`${product.clothingOptions.gallery.length} images`);
    }
  }

  // Custom options
  if (product.customOptions?.length) {
    summary.push(`${product.customOptions.length} custom options`);
  }

  return summary;
};

// Enhanced Metric Card Component
const MetricCard: React.FC<{
  icon: React.ComponentType<any>;
  title: string;
  value: string | number;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  gradient: string;
}> = ({ icon: Icon, title, value, change, changeType = 'neutral', gradient }) => (
  <GlassCard gradient={gradient} className="p-8 group">
    <div className="flex items-center justify-between mb-6">
      <motion.div
        className="p-4 bg-white/25 rounded-2xl border border-white/20 group-hover:bg-white/35 transition-all duration-300"
        whileHover={{ scale: 1.1, rotate: 5 }}
      >
        <Icon size={28} className="text-white drop-shadow-lg" />
      </motion.div>
      {change && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className={`flex items-center gap-2 px-3 py-2 rounded-xl border text-sm font-bold ${
            changeType === 'positive'
              ? 'text-green-200 bg-green-500/20 border-green-400/30' :
            changeType === 'negative'
              ? 'text-red-200 bg-red-500/20 border-red-400/30'
              : 'text-blue-200 bg-blue-500/20 border-blue-400/30'
          }`}
        >
          {change}
        </motion.div>
      )}
    </div>
    <div className="text-white">
      <motion.p
        className="text-4xl font-black mb-2 bg-gradient-to-r from-white to-yellow-200 bg-clip-text text-transparent"
        initial={{ scale: 0.8 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2 }}
      >
        {value}
      </motion.p>
      <p className="text-white/90 text-base font-semibold">{title}</p>
    </div>
  </GlassCard>
);

// Enhanced Quick Action Button Component
const QuickActionButton: React.FC<{
  icon: React.ComponentType<any>;
  label: string;
  onClick: () => void;
  gradient: string;
  isActive?: boolean;
}> = ({ icon: Icon, label, onClick, gradient, isActive = false }) => (
  <motion.button
    onClick={onClick}
    whileHover={{
      scale: 1.08,
      y: -8,
      rotateY: 5,
      boxShadow: "0 20px 40px -12px rgba(0, 0, 0, 0.3)"
    }}
    whileTap={{ scale: 0.95 }}
    transition={{ type: "spring", stiffness: 300, damping: 20 }}
    className={`relative group p-6 bg-gradient-to-br ${gradient} rounded-3xl shadow-2xl border border-white/30 overflow-hidden transform-gpu ${
      isActive ? 'ring-2 ring-white/50' : ''
    }`}
  >
    {/* Animated background overlay */}
    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 opacity-0 group-hover:opacity-100 transition-all duration-500 transform -translate-x-full group-hover:translate-x-full" />

    {/* Glow effect */}
    <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

    <div className="relative z-10 text-center">
      <motion.div
        className="mb-3 flex justify-center"
        whileHover={{ scale: 1.2, rotate: 10 }}
        transition={{ type: "spring", stiffness: 400, damping: 15 }}
      >
        <div className="p-3 bg-white/20 rounded-2xl border border-white/30">
          <Icon size={24} className="text-white drop-shadow-lg" />
        </div>
      </motion.div>
      <p className="text-white font-bold text-sm tracking-wide">{label}</p>
    </div>
  </motion.button>
);

// Enhanced Modal Component
const Modal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  gradient?: string;
}> = ({ isOpen, onClose, title, children, gradient = 'from-purple-600 to-blue-600' }) => (
  <AnimatePresence>
    {isOpen && (
      <>
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 50 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 50 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
        >
          <div className="relative bg-white rounded-3xl shadow-2xl max-w-md w-full overflow-hidden">
            {/* Header */}
            <div className={`bg-gradient-to-r ${gradient} p-6`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white/20 rounded-xl border border-white/30">
                    <Plus size={20} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-white text-xl font-bold">{title}</h3>
                    <p className="text-white/80 text-sm">Create a new product category</p>
                  </div>
                </div>

                <motion.button
                  onClick={onClose}
                  whileHover={{ scale: 1.1, rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  className="p-2 bg-white/20 rounded-xl border border-white/30 hover:bg-white/30 transition-colors"
                >
                  <X size={20} className="text-white" />
                </motion.button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              {children}
            </div>
          </div>
        </motion.div>
      </>
    )}
  </AnimatePresence>
);

// Category Chip Component
const CategoryChip: React.FC<{
  category: string;
  isSelected: boolean;
  isAll?: boolean;
  onSelect: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
}> = ({ category, isSelected, isAll = false, onSelect, onEdit, onDelete }) => (
  <motion.div
    whileHover={{ scale: 1.05, y: -2 }}
    whileTap={{ scale: 0.95 }}
    className={`relative group flex items-center gap-3 px-4 py-3 rounded-2xl border transition-all duration-300 cursor-pointer ${
      isSelected
        ? 'bg-gradient-to-r from-purple-600 to-blue-600 border-white/30 text-white shadow-lg'
        : 'bg-white/10 border-white/20 text-white/90 hover:bg-white/20'
    }`}
    onClick={onSelect}
  >
    <span className="font-semibold text-sm whitespace-nowrap">{category}</span>

    {!isAll && (
      <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <motion.button
          onClick={(e) => {
            e.stopPropagation();
            onEdit?.();
          }}
          whileHover={{ scale: 1.2 }}
          whileTap={{ scale: 0.8 }}
          className="p-1 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
        >
          <Edit3 size={12} className="text-white" />
        </motion.button>
        <motion.button
          onClick={(e) => {
            e.stopPropagation();
            onDelete?.();
          }}
          whileHover={{ scale: 1.2 }}
          whileTap={{ scale: 0.8 }}
          className="p-1 bg-red-500/20 rounded-lg hover:bg-red-500/30 transition-colors"
        >
          <Trash2 size={12} className="text-red-300" />
        </motion.button>
      </div>
    )}
  </motion.div>
);

// Product Card Component for List View
const ProductCard: React.FC<{ product: Product }> = ({ product }) => {
  const navigate = useNavigate();
  const hasDiscount = product.discountPrice && product.discountPrice > 0;
  const optionsCount = getProductOptionsCount(product);
  const optionsSummary = getProductOptionsSummary(product);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{
        scale: 1.02,
        y: -6,
        boxShadow: "0 20px 40px -12px rgba(0, 0, 0, 0.3)"
      }}
      transition={{ type: "spring", stiffness: 300, damping: 25 }}
      className="group relative bg-white/15 border border-white/30 rounded-3xl p-6 shadow-2xl overflow-hidden transform-gpu"
    >
      {/* Enhanced Shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/25 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1200" />

      {/* Subtle glow effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      <div className="relative z-10 flex gap-6">
        {/* Product Image */}
        <div className="relative">
          <motion.img
            src={product.image}
            alt={product.name}
            className={`w-24 h-24 rounded-2xl object-cover border-2 ${
              optionsCount > 0 ? 'border-green-400' : 'border-white/20'
            }`}
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          />
          {optionsCount > 0 && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-xl border-2 border-white shadow-lg"
            >
              {optionsCount}
            </motion.div>
          )}
        </div>

        {/* Product Info */}
        <div className="flex-1 flex flex-col justify-between">
          <div>
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-white text-xl font-bold">{product.name}</h4>
              {optionsCount === 0 && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="bg-orange-500 text-white text-xs font-bold px-3 py-1 rounded-xl"
                >
                  NEEDS SETUP
                </motion.div>
              )}
            </div>

            <p className="text-white/80 text-sm mb-3 flex items-center gap-2">
              <Tag size={14} />
              {product.category}
            </p>

            {/* Price */}
            <div className="flex items-center gap-3 mb-3">
              <span className={`text-2xl font-bold ${hasDiscount ? 'text-red-400' : 'text-green-400'}`}>
                ₪{product.price}
              </span>
              {hasDiscount && (
                <span className="text-white/60 line-through text-lg">
                  ₪{product.discountPrice}
                </span>
              )}
            </div>

            {/* Options Summary */}
            {optionsCount > 0 ? (
              <div className="mb-4">
                <p className="text-green-400 text-sm font-semibold mb-1">
                  ✅ {optionsCount} options configured
                </p>
                <p className="text-white/70 text-xs">
                  {optionsSummary.join(' • ')}
                </p>
              </div>
            ) : (
              <p className="text-orange-400 text-sm italic mb-4">
                ⚠️ No options configured
              </p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate(`/supplier/products/manage-options/${product.id}`)}
              className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-xl border border-white/20 hover:bg-purple-700 transition-colors font-semibold text-sm"
            >
              <Settings size={16} />
              Options
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center gap-2 px-4 py-2 bg-white/10 text-white rounded-xl border border-white/20 hover:bg-white/20 transition-colors font-semibold text-sm"
            >
              <Edit3 size={16} />
              Edit
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center gap-2 px-4 py-2 bg-red-500/20 text-red-300 rounded-xl border border-red-400/30 hover:bg-red-500/30 transition-colors font-semibold text-sm"
            >
              <Trash2 size={16} />
              Delete
            </motion.button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Product Card Component for Grid View
const ProductCardGrid: React.FC<{ product: Product }> = ({ product }) => {
  const navigate = useNavigate();
  const hasDiscount = product.discountPrice && product.discountPrice > 0;
  const optionsCount = getProductOptionsCount(product);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{
        scale: 1.05,
        y: -8,
        boxShadow: "0 20px 40px -12px rgba(0, 0, 0, 0.3)"
      }}
      transition={{ type: "spring", stiffness: 300, damping: 25 }}
      className="group relative bg-white/15 border border-white/30 rounded-3xl p-6 shadow-2xl overflow-hidden transform-gpu h-80"
    >
      {/* Enhanced Shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/25 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1200" />

      {/* Subtle glow effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      <div className="relative z-10 flex flex-col h-full">
        {/* Product Image */}
        <div className="relative flex justify-center mb-4">
          <motion.img
            src={product.image}
            alt={product.name}
            className={`w-20 h-20 rounded-2xl object-cover border-2 ${
              optionsCount > 0 ? 'border-green-400' : 'border-white/20'
            }`}
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          />
          {optionsCount > 0 && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-xl border-2 border-white shadow-lg"
            >
              {optionsCount}
            </motion.div>
          )}
        </div>

        {/* Product Info */}
        <div className="flex-1 flex flex-col justify-between text-center">
          <div>
            <h4 className="text-white text-lg font-bold mb-2 line-clamp-2">{product.name}</h4>

            <p className="text-white/80 text-sm mb-3 flex items-center justify-center gap-2">
              <Tag size={12} />
              {product.category}
            </p>

            {/* Price */}
            <div className="flex items-center justify-center gap-2 mb-3">
              <span className={`text-xl font-bold ${hasDiscount ? 'text-red-400' : 'text-green-400'}`}>
                ₪{product.price}
              </span>
              {hasDiscount && (
                <span className="text-white/60 line-through text-sm">
                  ₪{product.discountPrice}
                </span>
              )}
            </div>

            {/* Status */}
            {optionsCount > 0 ? (
              <p className="text-green-400 text-sm font-semibold mb-4">
                ✅ {optionsCount} options
              </p>
            ) : (
              <p className="text-orange-400 text-sm mb-4">
                ⚠️ No options
              </p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate(`/supplier/products/manage-options/${product.id}`)}
              className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-xl border border-white/20 hover:bg-purple-700 transition-colors font-semibold text-sm"
            >
              <Settings size={14} />
              Options
            </motion.button>

            <div className="flex gap-2">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex-1 flex items-center justify-center gap-1 px-3 py-2 bg-white/10 text-white rounded-xl border border-white/20 hover:bg-white/20 transition-colors font-semibold text-xs"
              >
                <Edit3 size={12} />
                Edit
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex-1 flex items-center justify-center gap-1 px-3 py-2 bg-red-500/20 text-red-300 rounded-xl border border-red-400/30 hover:bg-red-500/30 transition-colors font-semibold text-xs"
              >
                <Trash2 size={12} />
                Delete
              </motion.button>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

const SupplierProductsPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useCurrentUserData();
  const { products, setProducts, saveProducts, isSaving, lastSaved } = useSupplierProducts();
  const { categories, addCategory, deleteCategory, selectCategory, selectedCategory, renameCategory } = useSupplierCategories();

  // Enhanced state management
  const [newCatModal, setNewCatModal] = useState(false);
  const [newCat, setNewCat] = useState('');
  const [editCat, setEditCat] = useState<string | null>(null);
  const [editedValue, setEditedValue] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [showSummary, setShowSummary] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Find supplier data based on user's supplierId
  const supplierData = suppliersData.find((supplier) => supplier.id === user?.supplierId);

  useEffect(() => {
    if (supplierData?.products) {
      setProducts(supplierData.products);
    }
  }, [supplierData, setProducts]);

  // Save products function
  const handleSaveProducts = async () => {
    try {
      await saveProducts();
      // Show success notification (you can implement a toast system)
      console.log('Products saved successfully!');
    } catch (error) {
      console.error('Failed to save products:', error);
    }
  };

  // Get total options count across all products
  const getTotalOptionsCount = () => {
    return products.reduce((total, product) => total + getProductOptionsCount(product), 0);
  };

  const filteredCategories = products.reduce<string[]>((acc, product) =>
    acc.includes(product.category) ? acc : [...acc, product.category],
  []
  );

  useEffect(() => {
    // Add 'All' category if not present
    if (!categories.includes('All')) {
      addCategory('All');
    }
    // Add product categories if not present
    for (const category of filteredCategories) {
      if (!categories.includes(category)) {
        addCategory(category);
      }
    }
    // Select 'All' only if nothing is selected
    if (!selectedCategory) {
      selectCategory('All');
    }
  }, [products, categories, addCategory, selectCategory, selectedCategory, filteredCategories]);

  const filteredProducts = (selectedCategory && selectedCategory !== 'All')
    ? products.filter(p => p.category === selectedCategory)
    : products;

  const searchedProducts = searchQuery
    ? filteredProducts.filter(p =>
        p.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        p.category.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : filteredProducts;

  return (
    <>
      {/* Modern CSS Animations */}
      <style>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-15px) rotate(2deg); }
        }
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 30px rgba(139, 92, 246, 0.4); }
          50% { box-shadow: 0 0 60px rgba(139, 92, 246, 0.8); }
        }
        @keyframes pulse {
          0%, 100% { opacity: 0.8; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.08); }
        }
        @keyframes drift {
          0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
          25% { transform: translate(30px, -25px) rotate(2deg); }
          50% { transform: translate(-20px, 20px) rotate(-2deg); }
          75% { transform: translate(25px, 15px) rotate(1deg); }
        }
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>

      {/* Add Category Modal */}
      <Modal
        isOpen={newCatModal}
        onClose={() => setNewCatModal(false)}
        title="Add New Category"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-gray-700 text-sm font-semibold mb-2">
              Category Name
            </label>
            <input
              type="text"
              placeholder="e.g., Beverages, Main Dishes, Desserts"
              value={newCat}
              onChange={(e) => setNewCat(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition-all"
            />
            <p className="text-gray-500 text-xs mt-1">
              Choose a descriptive name for your product category
            </p>
          </div>

          <div className="flex gap-3 pt-2">
            <button
              onClick={() => {
                setNewCat('');
                setNewCatModal(false);
              }}
              className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors font-semibold"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                if (newCat.trim()) {
                  addCategory(newCat.trim());
                  setNewCat('');
                  setNewCatModal(false);
                }
              }}
              disabled={!newCat.trim()}
              className="flex-1 px-4 py-3 bg-purple-600 text-white rounded-xl hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-semibold flex items-center justify-center gap-2"
            >
              <CheckCircle2 size={16} />
              Add Category
            </button>
          </div>
        </div>
      </Modal>

      {/* Edit Category Modal */}
      <Modal
        isOpen={!!editCat}
        onClose={() => setEditCat(null)}
        title="Edit Category"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-gray-700 text-sm font-semibold mb-2">
              Category Name
            </label>
            <input
              type="text"
              value={editedValue}
              onChange={(e) => setEditedValue(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition-all"
            />
          </div>

          <div className="flex gap-3 pt-2">
            <button
              onClick={() => {
                setEditCat(null);
                setEditedValue('');
              }}
              className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors font-semibold"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                if (editCat && editedValue.trim()) {
                  renameCategory(editCat, editedValue);
                  setEditCat(null);
                  setEditedValue('');
                }
              }}
              className="flex-1 px-4 py-3 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-colors font-semibold flex items-center justify-center gap-2"
            >
              <Save size={16} />
              Save
            </button>
          </div>
        </div>
      </Modal>

      <div className="min-h-screen relative overflow-hidden">
        {/* Background - Behind everything */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          {/* Floating Orbs - Behind everything */}
          <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
          <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
          <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
          <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
          <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
          <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />

          {/* Particle System */}
          <ParticleSystem />

          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
        </div>

        {/* Main Content Container - Normal z-index to not interfere with header */}
        <div className="relative min-h-screen w-full p-8 pb-24" style={{ zIndex: 1 }}>
          <div className="w-full space-y-10">

            {/* Enhanced Modern Header Section */}
            <GlassCard gradient="from-white/25 to-white/15" className="p-10">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-6">
                  {/* Enhanced Store Icon */}
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ delay: 0.2, type: 'spring', damping: 15 }}
                    className="relative"
                  >
                    <motion.div
                      className="relative p-6 bg-white/25 border border-white/40 rounded-3xl"
                      whileHover={{ scale: 1.05, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 300, damping: 20 }}
                    >
                      <img
                        src={supplierData?.logoUrl}
                        alt={supplierData?.name}
                        className="w-12 h-12 rounded-xl object-cover"
                      />

                      {/* Enhanced Glow Effect */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-purple-400/40 to-blue-400/40 rounded-3xl blur-2xl"
                        animate={{ opacity: [0.3, 0.6, 0.3] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                    </motion.div>
                  </motion.div>

                  {/* Enhanced Welcome Text */}
                  <div>
                    <motion.h1
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                      className="text-white text-4xl font-black mb-3 bg-gradient-to-r from-white via-yellow-200 to-orange-200 bg-clip-text text-transparent"
                    >
                      {supplierData?.name || 'My Store'} Products
                      <motion.span
                        animate={{ rotate: [0, 20, -20, 0] }}
                        transition={{ duration: 1.5, repeat: Infinity, delay: 1 }}
                        className="inline-block ml-2"
                      >
                        📦
                      </motion.span>
                    </motion.h1>
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4 }}
                      className="flex items-center gap-6"
                    >
                      <motion.div
                        className="flex items-center gap-3 bg-white/15 px-4 py-2.5 rounded-2xl border border-white/30 shadow-lg"
                        whileHover={{ scale: 1.05 }}
                      >
                        <Package size={18} className="text-white/90" />
                        <span className="text-white text-base font-semibold">
                          Product Management Dashboard
                        </span>
                      </motion.div>
                      {lastSaved && (
                        <motion.div
                          className="flex items-center gap-3 bg-green-500/15 px-4 py-2.5 rounded-2xl border border-green-400/30 shadow-lg"
                          whileHover={{ scale: 1.05 }}
                        >
                          <Clock size={18} className="text-green-400" />
                          <span className="text-green-300 text-sm font-semibold">
                            Last saved: {lastSaved.toLocaleTimeString()}
                          </span>
                        </motion.div>
                      )}
                    </motion.div>
                  </div>
                </div>

                {/* Enhanced Save Button */}
                <motion.button
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 }}
                  whileHover={{
                    scale: 1.1,
                    rotate: 5,
                    boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.3)"
                  }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleSaveProducts}
                  disabled={isSaving}
                  className="group relative p-6 bg-white/25 border border-white/40 rounded-3xl hover:bg-white/35 transition-all duration-300 shadow-xl disabled:opacity-50"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <motion.div
                    className="relative z-10 flex items-center gap-3"
                    whileHover={{ rotate: isSaving ? 360 : 0 }}
                    transition={{ duration: 0.6 }}
                  >
                    {isSaving ? (
                      <Loader size={28} className="text-white drop-shadow-lg animate-spin" />
                    ) : (
                      <Save size={28} className="text-white drop-shadow-lg" />
                    )}
                    <span className="text-white font-bold text-lg">
                      {isSaving ? 'Saving...' : 'Save All'}
                    </span>
                  </motion.div>
                </motion.button>
              </div>
            </GlassCard>

            {/* Enhanced Metrics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-10">
              <MetricCard
                icon={Package}
                title="Total Products"
                value={products.length}
                change={`${searchedProducts.length} visible`}
                changeType="neutral"
                gradient="from-blue-500/25 to-indigo-500/25"
              />
              <MetricCard
                icon={Settings}
                title="Total Options"
                value={getTotalOptionsCount()}
                change={`${products.filter(p => getProductOptionsCount(p) > 0).length} configured`}
                changeType="positive"
                gradient="from-emerald-500/25 to-green-500/25"
              />
              <MetricCard
                icon={Tag}
                title="Categories"
                value={categories.length - 1} // Exclude 'All'
                change={selectedCategory !== 'All' ? `Viewing: ${selectedCategory}` : 'All categories'}
                changeType="neutral"
                gradient="from-yellow-500/25 to-orange-500/25"
              />
            </div>

            {/* Enhanced Quick Actions Section */}
            <GlassCard gradient="from-white/20 to-white/10" className="p-10 mt-10">
              <div className="flex items-center gap-4 mb-8">
                <motion.div
                  className="p-3 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl shadow-lg"
                  whileHover={{ scale: 1.1, rotate: 10 }}
                >
                  <Zap size={28} className="text-white" />
                </motion.div>
                <h3 className="text-white text-3xl font-black bg-gradient-to-r from-white to-yellow-200 bg-clip-text text-transparent">
                  Quick Actions & View Options
                </h3>
                <div className="flex-1 h-0.5 bg-gradient-to-r from-white/40 via-white/20 to-transparent"></div>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="p-2 bg-white/10 rounded-full"
                >
                  <Sparkles size={20} className="text-yellow-300" />
                </motion.div>
              </div>

              <div className="grid grid-cols-2 lg:grid-cols-5 gap-6">
                <QuickActionButton
                  icon={Plus}
                  label="Add Product"
                  onClick={() => navigate('/supplier/products/add')}
                  gradient="from-emerald-500 to-green-600"
                />
                <QuickActionButton
                  icon={viewMode === 'list' ? Grid3X3 : List}
                  label={viewMode === 'list' ? 'Grid View' : 'List View'}
                  onClick={() => setViewMode(viewMode === 'list' ? 'grid' : 'list')}
                  gradient="from-blue-500 to-indigo-600"
                  isActive={true}
                />
                <QuickActionButton
                  icon={BarChart3}
                  label={showSummary ? 'Hide Summary' : 'Show Summary'}
                  onClick={() => setShowSummary(!showSummary)}
                  gradient="from-purple-500 to-pink-600"
                  isActive={showSummary}
                />
                <QuickActionButton
                  icon={Filter}
                  label="Add Category"
                  onClick={() => setNewCatModal(true)}
                  gradient="from-orange-500 to-red-600"
                />
                <QuickActionButton
                  icon={Settings}
                  label="Bulk Actions"
                  onClick={() => console.log('Bulk actions')}
                  gradient="from-gray-500 to-gray-600"
                />
              </div>
            </GlassCard>

            {/* Search Bar */}
            <GlassCard gradient="from-white/15 to-white/10" className="p-6">
              <div className="flex items-center gap-4">
                <div className="relative flex-1">
                  <Search size={20} className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60" />
                  <input
                    type="text"
                    placeholder="Search products by name or category..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 bg-white/10 border border-white/20 rounded-2xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition-all"
                  />
                </div>
                {searchQuery && (
                  <motion.button
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setSearchQuery('')}
                    className="p-4 bg-red-500/20 border border-red-400/30 rounded-2xl text-red-300 hover:bg-red-500/30 transition-colors"
                  >
                    <X size={20} />
                  </motion.button>
                )}
              </div>
            </GlassCard>

            {/* Categories Section */}
            <GlassCard gradient="from-white/15 to-white/10" className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <motion.div
                  className="p-3 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl shadow-lg"
                  whileHover={{ scale: 1.1, rotate: -10 }}
                >
                  <Tag size={28} className="text-white" />
                </motion.div>
                <h3 className="text-white text-2xl font-black bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
                  Product Categories
                </h3>
                <div className="flex-1 h-0.5 bg-gradient-to-r from-white/40 via-white/20 to-transparent"></div>
              </div>

              <div className="flex flex-wrap gap-3">
                {categories.map((cat, i) => (
                  <CategoryChip
                    key={i}
                    category={cat}
                    isSelected={selectedCategory === cat}
                    isAll={cat === 'All'}
                    onSelect={() => selectCategory(cat)}
                    onEdit={() => {
                      setEditCat(cat);
                      setEditedValue(cat);
                    }}
                    onDelete={() => deleteCategory(cat)}
                  />
                ))}

                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setNewCatModal(true)}
                  className="flex items-center gap-2 px-4 py-3 bg-white/10 border border-white/20 text-white/90 rounded-2xl hover:bg-white/20 transition-all duration-300"
                >
                  <Plus size={16} />
                  <span className="font-semibold text-sm">Add Category</span>
                </motion.button>
              </div>
            </GlassCard>

            {/* Options Summary Section */}
            {showSummary && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              >
                <GlassCard gradient="from-white/20 to-white/10" className="p-8">
                  <div className="flex items-center gap-4 mb-6">
                    <motion.div
                      className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl shadow-lg"
                      whileHover={{ scale: 1.1, rotate: 10 }}
                    >
                      <BarChart3 size={28} className="text-white" />
                    </motion.div>
                    <h3 className="text-white text-2xl font-black bg-gradient-to-r from-white to-green-200 bg-clip-text text-transparent">
                      📊 Products Options Summary
                    </h3>
                  </div>

                  {products.length === 0 ? (
                    <div className="text-center py-12">
                      <Package size={64} className="text-white/30 mx-auto mb-4" />
                      <p className="text-white/60 text-lg">No products to summarize yet.</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {products.map((product, index) => {
                        const optionsCount = getProductOptionsCount(product);
                        const optionsSummary = getProductOptionsSummary(product);

                        return (
                          <motion.div
                            key={product.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="bg-white/10 border border-white/20 rounded-2xl p-6"
                          >
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <div className="flex items-center gap-3 mb-2">
                                  <h4 className="text-white text-lg font-bold">{product.name}</h4>
                                  <span className="text-white/60 text-sm">({product.category})</span>
                                </div>

                                {optionsCount > 0 ? (
                                  <div>
                                    <p className="text-green-400 text-sm font-semibold mb-1">
                                      {optionsCount} total options configured
                                    </p>
                                    <p className="text-white/70 text-sm">
                                      {optionsSummary.join(' • ')}
                                    </p>
                                  </div>
                                ) : (
                                  <p className="text-orange-400 text-sm italic">
                                    ⚠️ No options configured yet
                                  </p>
                                )}
                              </div>

                              <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => navigate(`/supplier/products/manage-options/${product.id}`)}
                                className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-xl border border-white/20 hover:bg-purple-700 transition-colors font-semibold text-sm"
                              >
                                <Settings size={16} />
                                Manage
                              </motion.button>
                            </div>
                          </motion.div>
                        );
                      })}

                      {/* Summary Statistics */}
                      <div className="border-t border-white/20 pt-6 mt-6">
                        <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-6">
                          <h4 className="text-white text-lg font-bold text-center mb-4">
                            📊 Summary Statistics
                          </h4>

                          <div className="grid grid-cols-3 gap-4">
                            <div className="text-center">
                              <p className="text-white/80 text-sm mb-1">With Options:</p>
                              <p className="text-white text-2xl font-bold">
                                {products.filter(p => getProductOptionsCount(p) > 0).length}
                              </p>
                            </div>

                            <div className="text-center">
                              <p className="text-white/80 text-sm mb-1">Need Setup:</p>
                              <p className="text-white text-2xl font-bold">
                                {products.filter(p => getProductOptionsCount(p) === 0).length}
                              </p>
                            </div>

                            <div className="text-center">
                              <p className="text-white/80 text-sm mb-1">Completion:</p>
                              <p className="text-white text-2xl font-bold">
                                {Math.round((products.filter(p => getProductOptionsCount(p) > 0).length / products.length) * 100) || 0}%
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </GlassCard>
              </motion.div>
            )}

            {/* Products Section */}
            <GlassCard gradient="from-white/15 to-white/10" className="p-8">
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center gap-4">
                  <motion.div
                    className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg"
                    whileHover={{ scale: 1.1, rotate: -10 }}
                  >
                    <ShoppingBag size={28} className="text-white" />
                  </motion.div>
                  <div>
                    <h3 className="text-white text-2xl font-black bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                      {selectedCategory === 'All' ? 'All Products' : `${selectedCategory} Products`}
                    </h3>
                    <p className="text-white/80 text-sm">
                      {searchedProducts.length} product{searchedProducts.length !== 1 ? 's' : ''}
                      {searchQuery && ` matching "${searchQuery}"`}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setViewMode('list')}
                    className={`p-3 rounded-xl border transition-all ${
                      viewMode === 'list'
                        ? 'bg-purple-600 border-white/30 text-white'
                        : 'bg-white/10 border-white/20 text-white/70 hover:bg-white/20'
                    }`}
                  >
                    <List size={20} />
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setViewMode('grid')}
                    className={`p-3 rounded-xl border transition-all ${
                      viewMode === 'grid'
                        ? 'bg-purple-600 border-white/30 text-white'
                        : 'bg-white/10 border-white/20 text-white/70 hover:bg-white/20'
                    }`}
                  >
                    <Grid3X3 size={20} />
                  </motion.button>
                </div>
              </div>

              {searchedProducts.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center py-16"
                >
                  <Package size={80} className="text-white/30 mx-auto mb-6" />
                  <h4 className="text-white text-xl font-bold mb-2">
                    {searchQuery ? 'No products found' : 'No products yet'}
                  </h4>
                  <p className="text-white/60 mb-6">
                    {searchQuery
                      ? `No products match "${searchQuery}". Try a different search term.`
                      : 'Start by adding your first product to get started.'
                    }
                  </p>
                  {!searchQuery && (
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => navigate('/supplier/products/add')}
                      className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl border border-white/20 hover:from-purple-700 hover:to-blue-700 transition-all font-semibold mx-auto"
                    >
                      <Plus size={20} />
                      Add Your First Product
                    </motion.button>
                  )}
                </motion.div>
              ) : viewMode === 'list' ? (
                // List View
                <div className="space-y-6">
                  {searchedProducts.map((product, i) => (
                    <motion.div
                      key={product.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: i * 0.1 }}
                    >
                      <ProductCard product={product} />
                    </motion.div>
                  ))}
                </div>
              ) : (
                // Grid View
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {searchedProducts.map((product, i) => (
                    <motion.div
                      key={product.id}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: i * 0.1 }}
                    >
                      <ProductCardGrid product={product} />
                    </motion.div>
                  ))}
                </div>
              )}
            </GlassCard>
          </div>
        </div>

        {/* Floating Action Button */}
        <motion.button
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          whileHover={{
            scale: 1.1,
            rotate: 10,
            boxShadow: "0 20px 40px -12px rgba(139, 92, 246, 0.6)"
          }}
          whileTap={{ scale: 0.9 }}
          onClick={() => navigate('/supplier/products/add')}
          className="fixed bottom-8 right-8 p-6 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full shadow-2xl border border-white/30 z-20"
          style={{ zIndex: 20 }}
        >
          <Plus size={32} className="drop-shadow-lg" />
        </motion.button>
      </div>
    </>
  );
};

export default SupplierProductsPage;